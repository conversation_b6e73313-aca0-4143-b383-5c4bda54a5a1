<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Tính AHP - So S<PERSON>h Thẻ Tín Dụng</title>

    <!-- Bootstrap & FontAwesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet" />
    <link href="/static/css/criteria.css" rel="stylesheet" />
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-calculator me-2"></i>
                <PERSON><PERSON><PERSON>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#" onclick="goBack()">
                    <i class="fas fa-arrow-left me-1"></i>
                    Quay lại
                </a>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="display-5">Lập Ma Trận So Sánh Cặp Các Tiêu Chí</h1>
            <p class="lead mb-0">Nhập giá trị so sánh giữa các tiêu chí để tính toán trọng số AHP</p>
        </div>

        <!-- Info Card -->
        <div class="info-card">
            <div class="d-flex align-items-center">
                <i class="fas fa-info-circle info-icon"></i>
                <div>
                    <strong>Hướng dẫn:</strong> So sánh tầm quan trọng giữa các tiêu chí. 
                    Nếu tiêu chí A quan trọng hơn tiêu chí B, hãy nhập giá trị từ 1 đến 9. 
                    Hệ thống sẽ tự động tính toán giá trị nghịch đảo cho ô đối xứng.
                </div>
            </div>
            <h5>Thang đo AHP (1-9)</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="scale-item">
                        <span>Quan trọng như nhau</span>
                        <span class="scale-value">1</span>
                    </div>
                    <div class="scale-item">
                        <span>Quan trọng hơn</span>
                        <span class="scale-value">3</span>
                    </div>
                    <div class="scale-item">
                        <span>Quan trọng nhiều hơn</span>
                        <span class="scale-value">5</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="scale-item">
                        <span>Rất quan trọng hơn</span>
                        <span class="scale-value">7</span>
                    </div>
                    <div class="scale-item">
                        <span>Vô cùng quan trọng</span>
                        <span class="scale-value">9</span>
                    </div>
                    <div class="scale-item">
                        <span>Giá trị trung gian</span>
                        <span class="scale-value">2,4,6,8</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Matrix Container -->
        <div class="matrix-container">
            <div class="matrix-header">
                <h3>
                    Ma Trận So Sánh Cặp Tiêu Chí
                    <span class="help-icon" title="Ma trận so sánh cặp được sử dụng trong phương pháp AHP để xác định trọng số của các tiêu chí">?</span>
                </h3>
            </div>

            <div class="table-responsive">
                <table class="matrix-table">
                    <thead>
                        <tr id="criteria-header-row">
                            <th>Tiêu chí</th> 
                        </tr>
                    </thead>
                    <tbody id="matrix-body"></tbody>
                </table>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="btn btn-custom btn-back" onclick="goBack()">
                    <i class="fas fa-arrow-left"></i>
                    Quay lại
                </button>
                <button class="btn btn-custom btn-calculate" onclick="calculateAHP()">
                    <i class="fas fa-calculator"></i>
                    Tính toán
                </button>
            </div>
        </div>
        
        <!-- Results Section -->
        <div class="results-section mt-4" id="results-section" >
            <h2 class="results-header">Kết Quả Tính Toán AHP</h2>

            <!-- Consistency Ratio -->
            <div class="consistency-card">
                <h4 style="color: var(--success-color); margin-bottom: 1rem;">
                    <i class="fas fa-check-circle me-2"></i>
                    Giá trị riêng, CI, CR
                </h4>
                <div class="consistency-item">
                    <span>Lambda(max):</span>
                    <span class="consistency-value" id="lambda-max"></span>
                </div>
                <div class="consistency-item">
                    <span>Chỉ số nhất quán CI:</span>
                    <span class="consistency-value" id="ci"></span>
                </div>
                <div class="consistency-item">
                    <span>Tỷ số nhất quán CR:</span>
                    <span class="consistency-value" id="cr"></span>
                </div>
            </div>

            <!-- Criteria Weights Table -->
            <h4 style="color: var(--primary-color); margin-bottom: 1rem;">
                <i class="fas fa-chart-bar me-2"></i>
                Sắp xếp thứ tự tiêu chí
            </h4>
            <div class="table-responsive">
                <table class="criteria-weights-table">
                    <thead>
                        <tr>
                            <th>Tiêu chí</th>
                            <th>Trọng số</th>
                            <th>Xếp hạng</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- JS sẽ render nội dung ở đây -->
                    </tbody>
                </table>
            </div>

            <!-- Chart -->
            <div class="chart-container">
                <h5 class="chart-title">
                    <i class="fas fa-chart-bar me-2"></i>
                    Trọng số
                </h5>
                <!-- JS sẽ render các chart-bar ở đây -->
            </div>
            <div class="action-buttons-bottom">
                <button class="btn btn-primary" id="nextBtn2" onclick="goToNextStep2()" >
                    <i>→</i>
                    Tiếp tục so sánh ngân hàng theo tiêu chí
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/static/js/script.js"></script>
</body>
</html>