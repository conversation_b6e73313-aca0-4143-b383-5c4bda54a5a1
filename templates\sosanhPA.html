<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Tính AHP - So S<PERSON>h Thẻ Tín Dụng</title>

    <!-- Bootstrap & FontAwesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet" />
    <link href="/static/css/style.css" rel="stylesheet" />
    <link href="/static/css/criteria.css" rel="stylesheet" />
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-credit-card me-2"></i>
                So Sánh Thẻ Tín Dụng Ngân Hàng
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#banks">Ngân hàng</a>
                <a class="nav-link" href="#comparison">So sánh AHP</a>
                <a class="nav-link" href="#results">Kết quả</a>
            </div>
        </div>
    </nav>

    <div class="container my-4">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="display-5">Lập Ma Trận So Sánh Cặp Các Ngân Hàng Với Từng Tiêu Chí</h1>
            <p class="lead mb-0">Nhập giá trị so sánh giữa các ngân hàng theo từng tiêu chí để tính toán trọng số AHP</p>
        </div>

        <!-- Info Card -->
        <div class="info-card">
            <div class="d-flex align-items-center">
                <i class="fas fa-info-circle info-icon"></i>
                <div>
                    <strong>Hướng dẫn:</strong> So sánh tầm quan trọng giữa các tiêu chí. 
                    Nếu tiêu chí A quan trọng hơn tiêu chí B, hãy nhập giá trị từ 1 đến 9. 
                    Hệ thống sẽ tự động tính toán giá trị nghịch đảo cho ô đối xứng.
                </div>
            </div>
            <h5>Thang đo AHP (1-9)</h5>
            <div class="row">
                <div class="col-md-6">
                    <div class="scale-item">
                        <span>Quan trọng ngang bằng</span>
                        <span class="scale-value">1</span>
                    </div>
                    <div class="scale-item">
                        <span>Quan trọng yếu hơn</span>
                        <span class="scale-value">3</span>
                    </div>
                    <div class="scale-item">
                        <span>Quan trọng khá hơn</span>
                        <span class="scale-value">5</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="scale-item">
                        <span>Quan trọng mạnh hơn</span>
                        <span class="scale-value">7</span>
                    </div>
                    <div class="scale-item">
                        <span>Quan trọng tuyệt đối</span>
                        <span class="scale-value">9</span>
                    </div>
                    <div class="scale-item">
                        <span>Giá trị trung gian</span>
                        <span class="scale-value">2,4,6,8</span>
                    </div>
                </div>
            </div>
        </div>

        <div id="matrices-container"></div>

        <div class="action-buttons-bottom" style="display: flex; justify-content: flex-end;">
            <button class="btn btn-primary" id="nextBtn2" onclick="goToNextStep3()">
                <i>→</i> Kết quả AHP
            </button>
        </div>
    </div>
    
    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p>&copy; 2024 AHP Bank Comparison System. Phát triển bằng Python Flask & AHP Algorithm.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="/static/js/scriptAHP_PA.js"></script>
    
</body>
</html>