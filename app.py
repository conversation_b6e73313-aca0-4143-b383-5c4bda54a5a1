import json
from flask import Flask, jsonify, render_template, request, session
from ahp.db import conn, get_criteria, get_latest_bank_weights, get_pairwise_matrix, query_banks_from_db, save_bank_weights_to_db, save_criteria_to_db
from ahp.calculation import calculate_ahp
# from ahp.export import export_excel_file, export_pdf_file
from flask import Flask, render_template
import pyodbc
from flask_session import Session

from ahp.export import export_ahp_pdf

app = Flask(__name__)
app.secret_key = 'mysecretkey1234567890'
app.config['SESSION_TYPE'] = 'filesystem'
Session(app)

def get_db_connection():
    conn = pyodbc.connect('DRIVER={ODBC Driver 17 for SQL Server};SERVER=MINHTAN;DATABASE=SoSanhTheTinDung;Trusted_Connection=yes;')
    return conn

@app.route('/')
def index():
    banks = query_banks_from_db()
    return render_template('index.html', banks = banks)

@app.route('/api/banks')
def api_banks():
    banks = query_banks_from_db()
    return jsonify(banks)

@app.route('/api/criteria')
def api_criteria():
    data = get_criteria()
    return jsonify(data)

@app.route('/api/save-session-data', methods=['POST'])
def save_session_data():
    data = request.get_json()
    try:
        import json
        session['selectedCriteria'] = json.dumps(data.get('selectedCriteria', []))
        session['criteriaData'] = json.dumps(data.get('criteriaData', {}))
        session['criteriaWeights'] = json.dumps(data.get('criteriaWeights', []))
        session['criteriaMatrix'] = json.dumps(data.get('criteriaMatrix', []))
        session['ranked_alternatives'] = data.get('rankedAlternatives', [])
        session['criteriaLambdaMax'] = data.get('criteriaLambdaMax', 0)
        session['criteriaCI'] = data.get('criteriaCI', 0)
        session['criteriaCR'] = data.get('criteriaCR', 0)
        ahp_matrices = data.get('ahpMatrix_', [])
        for i, matrix in enumerate(ahp_matrices):
            session[f"ahpMatrix_{i}"] = json.dumps(matrix)
        ahp_weights = data.get('ahpWeights_', [])
        for i, weights in enumerate(ahp_weights):
            session[f"ahpWeights_{i}"] = json.dumps(weights)
        session['bankData'] = json.dumps(data.get('bankData', []))
        return jsonify({"success": True})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/save-session-data-bank', methods=['POST'])
def save_session_data_bank():
    data = request.get_json()
    try:
        session['selectedCriteria'] = data.get('selectedCriteria', [])
        session['criteriaData'] = data.get('criteriaData', {})
        session['selectedBanks'] = data.get('selectedBanks', [])
        session['bankData'] = data.get('bankData', [])
        session.modified = True  # Đánh dấu session đã thay đổi
        return jsonify({"success": True})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/export_pdf')
def export_pdf_route():
    return export_ahp_pdf()

@app.route('/tinh-ahp')
def tinh_ahp():
    return render_template('tinh_ahp.html')

@app.route('/sosanh-PA')
def sosanh_pa():
    return render_template('sosanhPA.html')

@app.route('/ketqua')
def ketqua():
    return render_template('ketqua.html')

@app.route('/api/save-tieuchi', methods=['POST'])
def save_tieuchi():
    try:
        data = request.get_json()
        criteria = data['criteria']
        lan_tinh = data.get('lanTinh', 1)
        save_criteria_to_db(criteria, lan_tinh)
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
    
@app.route('/api/save-phuongan', methods=['POST'])
def save_phuongan():
    try:
        data = request.get_json()
        criteria = data['criteria']
        banks = data['banks']           # danh sách nhiều ngân hàng
        weights = data['weights']       # danh sách trọng số tương ứng
        lan_tinh = data.get('lanTinh', 1)
        save_bank_weights_to_db(criteria, banks, weights, lan_tinh)
        print("Received data:", data)
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500
    
@app.route('/api/save-final-scores', methods=['POST'])
def save_final_scores():
    try:
        data = request.get_json()
        print("Received data for save-final-scores:", data)  # In log
        scores = data.get('scores', [])
        lan_tinh = data.get('lanTinh', 1)

        if not scores:
            return jsonify({'success': False, 'error': 'Không có dữ liệu điểm số để lưu'}), 400

        cursor = conn.cursor()

        insert_data = []
        for idx, item in enumerate(scores, 1):
            insert_data.append((
                idx,           # ThuHang
                item['name'],  # NganHang
                item['score'], # TongDiem
                lan_tinh       # LanTinh
            ))

        if not insert_data:
            return jsonify({'success': False, 'error': 'Không có dữ liệu insert'}), 400

        sql = """
            INSERT INTO TongDiemNganHang (ThuHang, NganHang, TongDiem, LanTinh)
            VALUES (?, ?, ?, ?)
        """

        cursor.executemany(sql, insert_data)
        conn.commit()

        return jsonify({'success': True})

    except Exception as e:
        conn.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500
    
@app.route('/api/bank-weights-latest', methods=['GET'])
def api_bank_weights_latest():
    try:
        data = get_latest_bank_weights(conn)
        return jsonify({'success': True, 'data': data})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/get-final-scores', methods=['GET'])
def get_final_scores():
    lan_tinh = request.args.get('lanTinh', default=1, type=int)
    conn = get_db_connection()
    try:
        cursor = conn.cursor()
        sql = """
            SELECT ThuHang, NganHang, TongDiem
            FROM TongDiemNganHang
            WHERE LanTinh = ?
            ORDER BY ThuHang ASC
        """
        cursor.execute(sql, (lan_tinh,))
        rows = cursor.fetchall()
        cursor.close()  # Đóng cursor ngay sau khi dùng xong

        result = []
        for row in rows:
            result.append({
                'rank': row[0],
                'bank': row[1],
                'score': row[2]
            })
        return jsonify({'success': True, 'data': result})

    except Exception as e:
        # Đảm bảo rollback nếu cần
        conn.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500


if __name__ == '__main__':
    app.run(debug=True)

