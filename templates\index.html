<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>So Sánh Thẻ Tín Dụng <PERSON>c <PERSON> Hàng - AHP</title>

    <!-- Bootstrap & FontAwesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet" />
    <link href="/static/css/style.css" rel="stylesheet" />
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-credit-card me-2"></i>
                So Sánh Thẻ Tín Dụng Ngân Hàng
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="#banks">Ngân hàng</a>
                <a class="nav-link" href="#comparison">So sánh AHP</a>
                <a class="nav-link" href="#results">Kết quả</a>
            </div>
        </div>
    </nav>

    <div class="container my-5">
        <!-- Header Section -->
        <div class="text-center mb-5">
            <h1 class="display-4 text-primary">So Sánh Thẻ Tín Dụng Các Ngân Hàng</h1>
            <p class="lead">Sử dụng phương pháp AHP (Analytic Hierarchy Process) để lựa chọn ngân hàng phù hợp</p>
        </div>


        <section class="mb-4">
            <div class="filter-section">
                <div class="filter-header">
                    <h4 class="mb-0">
                        <i class="fas fa-filter me-2"></i>
                        Bộ lọc tìm kiếm
                    </h4>
                    <button class="filter-toggle" onclick="toggleFilter()">
                        <i class="fas fa-chevron-down" id="filterIcon"></i>
                    </button>
                </div>
                
                <div class="filter-content" id="filterContent">
                    <!-- Search Box -->
                    <div class="filter-group">
                        <label class="form-label">Tìm kiếm ngân hàng:</label>
                        <input type="text" class="form-control" id="searchInput" placeholder="Nhập tên ngân hàng..." onkeyup="applyFilters()">
                    </div>
                    
                    <div class="row">
                        <!-- Credit Limit Filter -->
                        <div class="col-md-4">
                            <div class="filter-group">
                                <label class="form-label">Hạn mức tín dụng:</label>
                                <div class="filter-chips">
                                    <span class="filter-chip" onclick="toggleChip(this, 'creditLimit', 'low')" data-value="low">Dưới 300 triệu</span>
                                    <span class="filter-chip" onclick="toggleChip(this, 'creditLimit', 'medium')" data-value="medium">300 - 500 triệu</span>
                                    <span class="filter-chip" onclick="toggleChip(this, 'creditLimit', 'high')" data-value="high">Trên 500 triệu</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Annual Fee Filter -->
                        <div class="col-md-4">
                            <div class="filter-group">
                                <label class="form-label">Phí thường niên:</label>
                                <div class="filter-chips">
                                    <span class="filter-chip" onclick="toggleChip(this, 'annualFee', 'free')" data-value="free">Miễn phí</span>
                                    <span class="filter-chip" onclick="toggleChip(this, 'annualFee', 'low')" data-value="low">Dưới 200.000</span>
                                    <span class="filter-chip" onclick="toggleChip(this, 'annualFee', 'high')" data-value="high">Trên 200.000</span>
                                </div>
                            </div>
                        </div> 

                        <!-- Bank Type Filter -->
                        <div class="col-md-4">
                            <div class="filter-group">
                                <label class="form-label">Loại ngân hàng:</label>
                                <div class="filter-chips">
                                    <span class="filter-chip" onclick="toggleChip(this, 'bankType', 'Ngân hàng nhà nước')" data-value="Ngân hàng nhà nước">Ngân hàng nhà nước</span>
                                    <span class="filter-chip" onclick="toggleChip(this, 'bankType', 'Ngân hàng tư nhân')" data-value="Ngân hàng tư nhân">Ngân hàng tư nhân</span>
                                    <span class="filter-chip" onclick="toggleChip(this, 'bankType', 'Ngân hàng nước ngoài')" data-value="Ngân hàng nước ngoài">Ngân hàng nước ngoài</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Filter Actions -->
                    <div class="text-center mt-3">
                        <button class="btn btn-outline-secondary btn-sm me-2" onclick="clearAllFilters()">
                            <i class="fas fa-times me-1"></i>
                            Xóa bộ lọc
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="applyFilters()">
                            <i class="fas fa-search me-1"></i>
                            Áp dụng
                        </button>
                    </div>
                </div>
            </div>
        </section>
        <!-- Banks Overview Section -->
        <section id="comparison" class="mb-5">
            <div class="card card-custom">
                <div class="card-header bg-primary text-white">
                    <h3 class="h3 mb-4">
                        <i class="fa-solid fa-building-columns"></i>
                        Thẻ tín dụng phổ biến
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Bắt đầu phần chọn ngân hàng từ file 1 -->
                    <div class="info-note">
                        <i>ℹ️</i>
                        Chọn từ 2-5 ngân hàng để thực hiện so sánh. Số lượng ngân hàng ít hơn sẽ cho kết quả chính xác hơn trong phương pháp AHP.
                    </div>

                    <div class="bank-grid" id="bankGrid">
                    </div>
                    <div class="d-flex justify-content-center mt-3">
                        <ul class="pagination" id="paginationControls"></ul>
                    </div>

                    <!-- Selection Summary -->
                    <div class="selection-summary">
                        <div class="summary-title">
                            <i>✓</i>
                            Ngân hàng đã chọn (<span id="selectedBankCount">0</span>/5)
                        </div>
                        <div class="selected-banks" id="selectedBanks">
                            <div class="no-selection">Chưa chọn ngân hàng nào</div>
                        </div>
                    </div>
                    <button class="btn btn-secondary" onclick="clearBankSelection()" >
                        <i>🔄</i>
                        Xóa tất cả
                    </button>
                    <!-- Kết thúc phần chọn ngân hàng -->
                </div>
            </div>
        </section>

        <!-- AHP Comparison Section -->
        <section id="comparison" class="mb-5">
            <div class="card card-custom">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="fas fa-balance-scale me-2"></i>
                        Tiêu chí đánh giá ngân hàng
                    </h3>
                </div>
                <div class="card-body">
                    <div class="info-note">
                        <i>ℹ️</i>
                        Chọn từ 3-5 tiêu chí để thực hiện so sánh. Số lượng tiêu chí ít hơn sẽ cho kết quả chính xác hơn trong phương pháp AHP.
                    </div>

                    <div class="criteria-grid" id="criteriaGrid">
                        <!-- Tiêu chí Lãi suất -->
                        <div class="criteria-card" data-criteria="interest_rate" data-importance="high" onclick="toggleCriteria(this)">
                            <div class="criteria-header">
                                <div class="criteria-icon">
                                    <i class="fas fa-percentage"></i>
                                </div>
                                <div class="criteria-title">Lãi Suất</div>
                            </div>
                            <div class="criteria-body">
                                <div class="criteria-description">
                                    Lãi suất áp dụng cho việc rút tiền mặt, trả chậm và các giao dịch khác. Lãi suất thấp giúp tiết kiệm chi phí.
                                </div>
                            </div>
                        </div>

                        <!-- Tiêu chí Phí thường niên -->
                        <div class="criteria-card" data-criteria="annual_fee" data-importance="high" onclick="toggleCriteria(this)">
                            <div class="criteria-header">
                                <div class="criteria-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="criteria-title">Phí Thường Niên</div>
                            </div>
                            <div class="criteria-body">
                                <div class="criteria-description">
                                    Chi phí cần thanh toán hàng năm để duy trì thẻ. Một số thẻ miễn phí năm đầu hoặc có điều kiện miễn phí.
                                </div>
                            </div>
                        </div>

                        <!-- Tiêu chí Hạn mức tín dụng -->
                        <div class="criteria-card" data-criteria="credit_limit" data-importance="medium" onclick="toggleCriteria(this)">
                            <div class="criteria-header">
                                <div class="criteria-icon">
                                    <i class="fas fa-credit-card"></i>
                                </div>
                                <div class="criteria-title">Hạn Mức Tín Dụng</div>
                            </div>
                            <div class="criteria-body">
                                <div class="criteria-description">
                                    Số tiền tối đa có thể chi tiêu bằng thẻ tín dụng. Hạn mức cao giúp linh hoạt hơn trong chi tiêu lớn.
                                </div>
                            </div>
                        </div>

                        <!-- Tiêu chí Chương trình ưu đãi -->
                        <div class="criteria-card" data-criteria="rewards_program" data-importance="high" onclick="toggleCriteria(this)">
                            <div class="criteria-header">
                                <div class="criteria-icon">
                                    <i class="fas fa-gift"></i>
                                </div>
                                <div class="criteria-title">Chương Trình Ưu Đãi</div>
                            </div>
                            <div class="criteria-body">
                                <div class="criteria-description">
                                    Hoàn tiền, tích điểm, tích dặm bay và các ưu đãi khác. Giúp tiết kiệm chi phí và nhận lợi ích từ việc chi tiêu.
                                </div>
                            </div>
                        </div>

                        <!-- Tiêu chí Dịch vụ khách hàng -->
                        <div class="criteria-card" data-criteria="customer_service" data-importance="medium" onclick="toggleCriteria(this)">
                            <div class="criteria-header">
                                <div class="criteria-icon">
                                    <i class="fas fa-headset"></i>
                                </div>
                                <div class="criteria-title">Dịch Vụ Khách Hàng</div>
                            </div>
                            <div class="criteria-body">
                                <div class="criteria-description">
                                    Chất lượng hỗ trợ, tư vấn và giải quyết vấn đề. Bao gồm hotline 24/7, hỗ trợ trực tuyến và dịch vụ tại quầy.
                                </div>
                            </div>
                        </div>

                        <!-- Tiêu chí Thương hiệu -->
                        <div class="criteria-card" data-criteria="brand_reputation" data-importance="medium" onclick="toggleCriteria(this)">
                            <div class="criteria-header">
                                <div class="criteria-icon">
                                    <i class="fas fa-star"></i>
                                </div>
                                <div class="criteria-title">Thương Hiệu & Uy Tín</div>
                            </div>
                            <div class="criteria-body">
                                <div class="criteria-description">
                                    Danh tiếng và độ tin cậy của ngân hàng. Mức độ chấp nhận thẻ tại các merchant trong và ngoài nước.
                                </div>
                            </div>
                        </div>

                        <!-- Tiêu chí Điều kiện phát hành -->
                        <div class="criteria-card" data-criteria="approval_requirements" data-importance="low" onclick="toggleCriteria(this)">
                            <div class="criteria-header">
                                <div class="criteria-icon">
                                    <i class="fas fa-file-contract"></i>
                                </div>
                                <div class="criteria-title">Điều Kiện Phát Hành</div>
                            </div>
                            <div class="criteria-body">
                                <div class="criteria-description">
                                    Yêu cầu về thu nhập, tài liệu, thời gian xét duyệt. Độ dễ dàng trong việc đăng ký và nhận thẻ.
                                </div>
                            </div>
                        </div>
                    </div>
                <!-- Selection Summary -->
                <div class="selection-summary">
                    <div class="summary-title">
                        <i>✓</i>
                        Tiêu chí đã chọn (<span id="selectedCriteriaCount">0</span>/5)
                    </div>
                    <div class="selected-criteria" id="selectedCriteriaList">
                        <div class="no-selection">Chưa chọn tiêu chí nào</div>
                    </div>
                </div>
                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button class="btn btn-secondary" onclick="clearCriteriaSelection()">
                        <i>🔄</i>
                        Xóa tất cả
                    </button>
                    <button class="btn btn-primary" id="nextBtn" onclick="goToNextStep()" disabled>
                        <i>→</i>
                        Thực hiện tính toán AHP
                    </button>
                </div>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p>&copy; 2024 AHP Bank Comparison System. Phát triển bằng Python Flask & AHP Algorithm.</p>
        </div>
    </footer>

    <!-- Bootstrap & FontAwesome JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="/static/js/bank.js"></script>
</body>
</html>
