<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ma Trận So <PERSON><PERSON>h Ngân Hàng - Phân Tích AHP</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" >
    <link href="/static/css/ketqua.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏦 Hệ Thống Phân Tích So Sánh Ngân Hàng</h1>
            <p>Áp dụng phương pháp AHP (Analytic Hierarchy Process) để đánh giá và xếp hạng các ngân hàng</p>
        </div>

        <!-- Trọng số tiêu chí -->
        <div class="section">
            <div class="section-title">📊 Trọng Số Các Tiêu Chí <PERSON></div>
            <div id="criteria-weights-container" class="weights-grid">
                <!-- Trọng số tiêu chí sẽ được render vào đây từ JS -->
            </div>
            <div class="criteria-description">
                <h4>💡 Giải thích trọng số:</h4>
                <p>Lãi xuất được đánh giá là yếu tố quan trọng nhất (51.30%) vì ảnh hưởng trực tiếp đến chi phí tài chính của khách hàng. Phí thường niên đứng thứ hai (26.78%) do tác động đến chi phí duy trì tài khoản hàng năm.</p>
            </div>
        </div>

        <!-- Ma trận so sánh chi tiết -->
        <div class="section">
            <div class="section-title">🔍 Ma Trận Điểm Số Chi Tiết Theo Từng Tiêu Chí</div>
            
            <div id="bank-criteria-weights-container" class="weights-grid">
                <!-- Trọng số tiêu chí sẽ được render vào đây từ JS -->
            </div>
        </div>

        <div class="section" style="display: flex; justify-content: center; align-items: center;">
            <div style="flex: 1;">
                <div class="section-title">🏆 Kết Quả Tổng Hợp & Xếp Hạng</div>
                <table class="comparison-table" id="final-scores-table" style="width: 100%;">
                <thead>
                    <tr>
                    <th>Thứ Hạng</th>
                    <th>Ngân Hàng</th>
                    <th>Điểm Số Tổng Hợp</th>
                    <th>Đánh Giá</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Dữ liệu bảng sẽ được đổ vào đây -->
                </tbody>
                </table>
            </div>

            <canvas id="pieChart" width="100" height="100" style="display: block;"></canvas>


        </div>

         <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="btn-custom btn-pdf" id="btnExportPDF">
                <i class="fas fa-file-pdf"></i> <!-- icon PDF -->
                XUẤT FILE PDF
            </button>
        </div>
    </div>

    <script src="/static/js/ketqua.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</body>
</html>